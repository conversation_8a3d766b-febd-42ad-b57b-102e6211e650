"""
东方财富网-数据中心-重大合同-重大合同明细指标计算函数

基于akshare的stock_zdhtmx_em API，计算重大合同相关分析指标
"""

from datetime import datetime
from typing import Any

import akshare as ak
import pandas as pd


def calculate_stock_zdhtmx_em_indicators(
    start_date: str = "", end_date: str = "", target_date: str | None = None
) -> dict[str, Any]:
    """
    计算东方财富网-数据中心-重大合同-重大合同明细指标

    Args:
        start_date: 开始日期，格式为YYYYMMDD，默认为空字符串表示无限制
        end_date: 结束日期，格式为YYYYMMDD，默认为空字符串表示无限制
        target_date: 目标日期，格式为YYYYMMDD，如果为None则使用最新数据

    Returns:
        包含锚定日期和技术指标的字典，结构为:
        {
            'date': str,  # 最后数据点的日期，格式为YYYYMMDD
            'indicators': dict  # 包含各项技术指标的字典
        }

    Raises:
        ValueError: 当参数无效或数据获取失败时
        RuntimeError: 当指标计算失败时
    """
    try:
        # 获取重大合同明细数据
        df = ak.stock_zdhtmx_em(start_date=start_date, end_date=end_date)

        if df is None or df.empty:
            # 返回空指标而不是抛出异常
            indicators = _get_empty_indicators()
            return {"date": target_date or datetime.now().strftime("%Y%m%d"), "indicators": indicators}

        # 确保数据按公告日期排序
        if "公告日期" not in df.columns:
            # 如果没有公告日期列，返回空指标
            indicators = _get_empty_indicators()
            return {"date": target_date or datetime.now().strftime("%Y%m%d"), "indicators": indicators}

        df = df.sort_values("公告日期").reset_index(drop=True)

        # 如果指定了目标日期，筛选到目标日期为止的数据
        if target_date:
            target_datetime = pd.to_datetime(target_date, format="%Y%m%d")
            df["公告日期"] = pd.to_datetime(df["公告日期"])
            df = df[df["公告日期"] <= target_datetime]

            if df.empty:
                raise ValueError(f"在目标日期 {target_date} 之前未找到重大合同数据")

        # 获取最后数据点的公告日期作为锚定时间
        last_date = df.iloc[-1]["公告日期"]
        if isinstance(last_date, str):
            anchor_date = last_date
        else:
            # 如果是datetime对象，转换为字符串格式
            anchor_date = last_date.strftime("%Y%m%d")

        # 计算各项指标
        indicators = {}

        # 1. 合同金额占上市公司上年度营业收入的比例
        if len(df) >= 1:
            indicators["contract_value_to_revenue_ratio"] = (
                _calculate_contract_value_to_revenue_ratio(df)
            )

        # 2. 合同金额占最新财务报表营业收入的比例
        if len(df) >= 1:
            indicators["contract_value_to_current_revenue_ratio"] = (
                _calculate_contract_value_to_current_revenue_ratio(df)
            )

        # 3. 根据合同金额占上年度营业收入的比例进行分类
        if len(df) >= 1:
            indicators["contract_size_category"] = _calculate_contract_size_category(df)

        # 4. 合同签署日期与公告日期的时间差（天数）
        if len(df) >= 1:
            indicators["contract_signing_to_announcement_delay"] = (
                _calculate_contract_signing_to_announcement_delay(df)
            )

        # 5. 与上市公司存在关联关系的签署主体所占合同金额的比例
        if len(df) >= 1:
            indicators["related_party_contract_ratio"] = (
                _calculate_related_party_contract_ratio(df)
            )

        # 6. 合同类型分布多样性指数
        if len(df) >= 1:
            indicators["contract_type_diversification_index"] = (
                _calculate_contract_type_diversification_index(df)
            )

        # 7. 合同金额相对于上年度营业收入的增长贡献率
        if len(df) >= 1:
            indicators["contract_value_growth_impact"] = (
                _calculate_contract_value_growth_impact(df)
            )

        # 8. 单位时间签署的重大合同数量（月度频率）
        if len(df) >= 1:
            indicators["contract_signing_frequency"] = (
                _calculate_contract_signing_frequency(df)
            )

        # 9. 前N大合同金额占总合同金额的比例
        if len(df) >= 1:
            indicators["large_contract_concentration_ratio"] = (
                _calculate_large_contract_concentration_ratio(df)
            )

        # 10. 合同金额与最新财务报表营业收入的比值（杠杆效应）
        if len(df) >= 1:
            indicators["contract_revenue_leverage_ratio"] = (
                _calculate_contract_revenue_leverage_ratio(df)
            )

        # 添加元数据到indicators字典
        indicators["start_date"] = start_date
        indicators["end_date"] = end_date
        indicators["calculation_date"] = target_date or datetime.now().strftime(
            "%Y%m%d"
        )
        indicators["data_points"] = len(df)

        # 返回新的结构：包含锚定日期和指标字典
        return {"date": anchor_date, "indicators": indicators}

    except Exception as e:
        raise RuntimeError(f"计算重大合同指标失败: {str(e)}")


def _calculate_contract_value_to_revenue_ratio(df: pd.DataFrame) -> float:
    """计算合同金额占上市公司上年度营业收入的比例
    公式: 合同金额 / 上年度营业收入 * 100
    """
    if len(df) < 1:
        return 0.0

    contract_amount = df["合同金额"].dropna().astype(float)
    revenue = df["上年度营业收入"].dropna().astype(float)

    if len(contract_amount) < 1 or len(revenue) < 1:
        return 0.0

    if revenue.iloc[0] == 0:
        return 0.0

    return (contract_amount.iloc[0] / revenue.iloc[0]) * 100


def _calculate_contract_value_to_current_revenue_ratio(df: pd.DataFrame) -> float:
    """计算合同金额占最新财务报表营业收入的比例
    公式: 合同金额 / 最新财务报表的营业收入 * 100
    """
    if len(df) < 1:
        return 0.0

    contract_amount = df["合同金额"].dropna().astype(float)
    current_revenue = df["最新财务报表的营业收入"].dropna().astype(float)

    if len(contract_amount) < 1 or len(current_revenue) < 1:
        return 0.0

    if current_revenue.iloc[0] == 0:
        return 0.0

    return (contract_amount.iloc[0] / current_revenue.iloc[0]) * 100


def _calculate_contract_size_category(df: pd.DataFrame) -> str:
    """根据合同金额占上年度营业收入的比例进行分类
    公式: if(占上年度营业收入比例 >= 50, '重大', if(占上年度营业收入比例 >= 10, '中等', '小额'))
    """
    if len(df) < 1:
        return "小额"

    ratio = df["占上年度营业收入比例"].dropna().astype(float)

    if len(ratio) < 1:
        return "小额"

    value = ratio.iloc[0]
    if value >= 50:
        return "重大"
    elif value >= 10:
        return "中等"
    else:
        return "小额"


def _calculate_contract_signing_to_announcement_delay(df: pd.DataFrame) -> float:
    """计算合同签署日期与公告日期的时间差（天数）
    公式: (公告日期 - 签署日期).days
    """
    if len(df) < 1:
        return 0.0

    signing_date = pd.to_datetime(df["签署日期"].iloc[0])
    announcement_date = pd.to_datetime(df["公告日期"].iloc[0])

    if pd.isna(signing_date) or pd.isna(announcement_date):
        return 0.0

    delay = (announcement_date - signing_date).days
    return float(delay)


def _calculate_related_party_contract_ratio(df: pd.DataFrame) -> float:
    """计算与上市公司存在关联关系的签署主体所占合同金额的比例
    公式: sum(if(签署主体-与上市公司关系 != '无', 合同金额, 0)) / sum(合同金额) * 100
    """
    if len(df) < 1:
        return 0.0

    contract_amount = df["合同金额"].dropna().astype(float)
    relationship = df["签署主体-与上市公司关系"].dropna()

    if len(contract_amount) < 1 or len(relationship) < 1:
        return 0.0

    total_amount = contract_amount.sum()
    if total_amount == 0:
        return 0.0

    # 使用布尔索引选择非"无"关系的行
    related_rows = relationship[relationship != "无"]
    related_indices = related_rows.index

    if len(related_indices) == 0:
        return 0.0

    # 计算关联方合同金额总和
    related_amount = contract_amount.iloc[related_indices].sum()

    return (related_amount / total_amount) * 100


def _calculate_contract_type_diversification_index(df: pd.DataFrame) -> float:
    """计算合同类型分布多样性指数
    公式: 1 - sum(合同金额^2) / (sum(合同金额))^2
    """
    if len(df) < 1:
        return 0.0

    contract_amount = df["合同金额"].dropna().astype(float)
    contract_type = df["合同类型"].dropna()

    if len(contract_amount) < 1 or len(contract_type) < 1:
        return 0.0

    total_amount = contract_amount.sum()
    if total_amount == 0:
        return 0.0

    # 计算每个类型的合同金额平方和
    amount_squared = (contract_amount**2).sum()

    index_value = 1 - (amount_squared / (total_amount**2))
    return index_value


def _calculate_contract_value_growth_impact(df: pd.DataFrame) -> float:
    """计算合同金额相对于上年度营业收入的增长贡献率
    公式: 合同金额 / 上年度营业收入
    """
    if len(df) < 1:
        return 0.0

    contract_amount = df["合同金额"].dropna().astype(float)
    revenue = df["上年度营业收入"].dropna().astype(float)

    if len(contract_amount) < 1 or len(revenue) < 1:
        return 0.0

    if revenue.iloc[0] == 0:
        return 0.0

    return contract_amount.iloc[0] / revenue.iloc[0]


def _calculate_contract_signing_frequency(df: pd.DataFrame) -> float:
    """计算单位时间签署的重大合同数量（月度频率）
    公式: count(合同金额 > 0) over (period=1 month)
    """
    if len(df) < 1:
        return 0.0

    # 这里简单返回总合同数作为示例，实际应按月度聚合
    contract_amount = df["合同金额"].dropna().astype(float)

    # 返回非零合同的数量（简化处理）
    valid_contracts = contract_amount[contract_amount > 0]
    return float(len(valid_contracts))


def _calculate_large_contract_concentration_ratio(df: pd.DataFrame) -> float:
    """计算前N大合同金额占总合同金额的比例
    公式: sum(top_n(合同金额, 3)) / sum(合同金额) * 100
    """
    if len(df) < 1:
        return 0.0

    contract_amount = df["合同金额"].dropna().astype(float)

    if len(contract_amount) < 1:
        return 0.0

    total_amount = contract_amount.sum()
    if total_amount == 0:
        return 0.0

    # 获取前3大合同金额
    top_3 = contract_amount.nlargest(3).sum()

    return (top_3 / total_amount) * 100


def _calculate_contract_revenue_leverage_ratio(df: pd.DataFrame) -> float:
    """计算合同金额与最新财务报表营业收入的比值（杠杆效应）
    公式: 合同金额 / 最新财务报表的营业收入
    """
    if len(df) < 1:
        return 0.0

    contract_amount = df["合同金额"].dropna().astype(float)
    current_revenue = df["最新财务报表的营业收入"].dropna().astype(float)

    if len(contract_amount) < 1 or len(current_revenue) < 1:
        return 0.0

    if current_revenue.iloc[0] == 0:
        return 0.0

    return contract_amount.iloc[0] / current_revenue.iloc[0]


def _get_empty_indicators() -> dict[str, Any]:
    """返回全None的指标字典，用于没有数据的情况"""
    return {
        "contract_value_to_revenue_ratio": None,
        "contract_value_to_current_revenue_ratio": None,
        "contract_size_category": None,
        "contract_count": None,
        "average_contract_value": None,
        "total_contract_value": None,
        "contract_concentration_ratio": None,
        "contract_growth_rate": None,
        "contract_revenue_leverage_ratio": None,
        "start_date": None,
        "end_date": None,
        "calculation_date": datetime.now().strftime("%Y%m%d"),
        "data_points": 0,
    }
