"""
东方财富-数据中心-年报季报-业绩快报-利润表指标计算函数

基于akshare的stock_lrb_em API，计算利润表相关财务指标
"""

from datetime import datetime
from typing import Any

import akshare as ak
import pandas as pd
from .....utils.symbol_utils import match_symbol_in_dataframe


def calculate_stock_lrb_em_indicators(
    symbol: str, date: str = "20240331", target_date: str | None = None
) -> dict[str, Any]:
    """
    计算东方财富-数据中心-年报季报-业绩快报-利润表相关财务指标

    Args:
        symbol: 股票代码，如 "SZ000002"
        date: 报告期，格式为"YYYYMMDD"，可选值包括{"20120331", "20120630", "20120930", "20121231"}等
        target_date: 目标交易日，格式为YYYYMMDD，如果为None则使用最新数据

    Returns:
        包含锚定日期和技术指标的字典，结构为:
        {
            'date': str,  # 最后数据点的日期，格式为YYYYMMDD
            'indicators': dict  # 包含各项技术指标的字典
        }

    Raises:
        ValueError: 当参数无效或数据获取失败时
        RuntimeError: 当指标计算失败时
    """
    # 参数验证
    if not symbol:
        raise ValueError("股票代码不能为空")
    if not date:
        raise ValueError("报告期不能为空")

    try:
        # 获取利润表数据
        df = ak.stock_lrb_em(date=date)

        if df.empty:
            raise ValueError(f"未获取到报告期 {date} 的利润表数据")

        # 根据股票代码过滤数据，支持三种格式匹配
        df_filtered = match_symbol_in_dataframe(df, "股票代码", symbol)

        if df_filtered.empty:
            # 如果没有找到对应股票的数据，返回全None指标
            indicators = _get_empty_indicators()
            return {"date": target_date or datetime.now().strftime("%Y%m%d"), "indicators": indicators}

        # 确保数据按股票代码排序
        df_filtered = df_filtered.sort_values("股票代码").reset_index(drop=True)

        # 如果指定了目标日期，筛选到目标日期为止的数据（这里主要用于兼容性）
        if target_date:
            # 注意：利润表数据没有明确的日期字段，所以此处仅作为占位符处理
            pass

        # 获取最后数据点的日期作为锚定时间（使用公告日期字段）
        last_announcement_date = df_filtered.iloc[-1]["公告日期"]
        if isinstance(last_announcement_date, str):
            anchor_date = last_announcement_date.replace("-", "")
        else:
            # 如果是datetime对象，转换为字符串格式
            anchor_date = last_announcement_date.strftime("%Y%m%d")

        # 计算各项财务指标
        indicators = {}

        # 使用第一行数据（应该只有一行，因为我们过滤了特定股票）
        if len(df_filtered) > 0:
            row = df_filtered.iloc[0]  # 取第一行数据

            # 1. 净利润率
            if pd.notna(row["净利润"]) and pd.notna(row["营业总收入"]):
                indicators["net_profit_margin"] = _calculate_net_profit_margin(row)

            # 2. 营业利润率
            if pd.notna(row["营业利润"]) and pd.notna(row["营业总收入"]):
                indicators["operating_profit_margin"] = _calculate_operating_profit_margin(row)

            # 3. EBITDA利润率
            required_fields = [
                "营业利润",
                "营业总支出-营业支出",
                "营业总支出-管理费用",
                "营业总支出-销售费用",
                "营业总支出-财务费用",
                "营业总收入",
            ]
            if all(pd.notna(row[field]) for field in required_fields):
                indicators["ebitda_margin"] = _calculate_ebitda_margin(row)

            # 4. 毛利率
            if pd.notna(row["营业总收入"]) and pd.notna(row["营业总支出-营业支出"]):
                indicators["gross_profit_margin"] = _calculate_gross_profit_margin(row)

            # 5. 利润质量比率
            if pd.notna(row["净利润"]) and pd.notna(row["营业利润"]):
                indicators["profit_quality_ratio"] = _calculate_profit_quality_ratio(row)

            # 6. 营业费用率
            required_fields = [
                "营业总支出-销售费用",
                "营业总支出-管理费用",
                "营业总支出-财务费用",
                "营业总收入",
            ]
            if all(pd.notna(row[field]) for field in required_fields):
                indicators["operating_expense_ratio"] = _calculate_operating_expense_ratio(row)

            # 7. 净利润同比增长率
            if pd.notna(row["净利润同比"]):
                indicators["net_profit_growth_rate"] = _calculate_net_profit_growth_rate(row)

            # 8. 营业收入同比增长率
            if pd.notna(row["营业总收入同比"]):
                indicators["revenue_growth_rate"] = _calculate_revenue_growth_rate(row)

            # 9. 利润杠杆指数
            required_fields = ["净利润同比", "营业总收入同比"]
            if all(pd.notna(row[field]) for field in required_fields):
                indicators["profit_leverage_index"] = _calculate_profit_leverage_index(row)

            # 10. 利润安全边际
            if pd.notna(row["营业利润"]) and pd.notna(row["净利润"]):
                indicators["profit_safety_margin"] = _calculate_profit_safety_margin(row)

        # 添加元数据到indicators字典
        indicators["report_date"] = date
        indicators["calculation_date"] = target_date or datetime.now().strftime(
            "%Y%m%d"
        )
        indicators["data_points"] = len(df)

        # 返回新的结构：包含锚定日期和指标字典
        return {"date": anchor_date, "indicators": indicators}

    except Exception as e:
        raise RuntimeError(f"计算利润表财务指标失败: {str(e)}")


def _calculate_net_profit_margin(row: pd.Series) -> float:
    """计算净利润率
    公式: 净利润 / 营业总收入 * 100
    """
    if row["营业总收入"] == 0:
        return 0.0

    return (row["净利润"] / row["营业总收入"]) * 100


def _calculate_operating_profit_margin(row: pd.Series) -> float:
    """计算营业利润率
    公式: 营业利润 / 营业总收入 * 100
    """
    if row["营业总收入"] == 0:
        return 0.0

    return (row["营业利润"] / row["营业总收入"]) * 100


def _calculate_ebitda_margin(row: pd.Series) -> float:
    """计算EBITDA利润率
    公式: (营业利润 + 营业总支出-管理费用 + 营业总支出-销售费用 + 营业总支出-财务费用) / 营业总收入 * 100
    """
    ebitda = (
        row["营业利润"]
        + row["营业总支出-管理费用"]
        + row["营业总支出-销售费用"]
        + row["营业总支出-财务费用"]
    )

    if row["营业总收入"] == 0:
        return 0.0

    return (ebitda / row["营业总收入"]) * 100


def _calculate_gross_profit_margin(row: pd.Series) -> float:
    """计算毛利率
    公式: (营业总收入 - 营业总支出-营业支出) / 营业总收入 * 100
    """
    gross_profit = row["营业总收入"] - row["营业总支出-营业支出"]

    if row["营业总收入"] == 0:
        return 0.0

    return (gross_profit / row["营业总收入"]) * 100


def _calculate_profit_quality_ratio(row: pd.Series) -> float:
    """计算利润质量比率
    公式: 净利润 / 营业利润 * 100
    """
    if row["营业利润"] == 0:
        return 0.0

    return (row["净利润"] / row["营业利润"]) * 100


def _calculate_operating_expense_ratio(row: pd.Series) -> float:
    """计算营业费用率
    公式: (营业总支出-销售费用 + 营业总支出-管理费用 + 营业总支出-财务费用) / 营业总收入 * 100
    """
    total_expenses = (
        row["营业总支出-销售费用"]
        + row["营业总支出-管理费用"]
        + row["营业总支出-财务费用"]
    )

    if row["营业总收入"] == 0:
        return 0.0

    return (total_expenses / row["营业总收入"]) * 100


def _calculate_net_profit_growth_rate(row: pd.Series) -> float:
    """计算净利润同比增长率
    公式: 净利润同比
    """
    return row["净利润同比"]


def _calculate_revenue_growth_rate(row: pd.Series) -> float:
    """计算营业收入同比增长率
    公式: 营业总收入同比
    """
    return row["营业总收入同比"]


def _calculate_profit_leverage_index(row: pd.Series) -> float:
    """计算利润杠杆指数
    公式: 净利润同比 / 营业总收入同比
    """
    if row["营业总收入同比"] == 0:
        return 0.0

    return row["净利润同比"] / row["营业总收入同比"]


def _calculate_profit_safety_margin(row: pd.Series) -> float:
    """计算利润安全边际
    公式: (营业利润 - 净利润) / 净利润 * 100
    """
    if row["净利润"] == 0:
        return 0.0

    return ((row["营业利润"] - row["净利润"]) / row["净利润"]) * 100
