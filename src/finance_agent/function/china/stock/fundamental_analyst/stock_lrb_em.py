"""
东方财富-数据中心-年报季报-业绩快报-利润表指标计算函数

基于akshare的stock_lrb_em API，计算利润表相关财务指标
"""

from datetime import datetime
from typing import Any

import akshare as ak
import pandas as pd
from .....utils.symbol_utils import match_symbol_in_dataframe


def calculate_stock_lrb_em_indicators(
    symbol: str, target_date: str | None = None
) -> dict[str, Any]:
    """
    计算东方财富-数据中心-年报季报-业绩快报-利润表相关财务指标

    Args:
        symbol: 股票代码，如 "SZ000002"
        target_date: 目标交易日，格式为YYYYMMDD，如果为None则使用最新数据

    Returns:
        包含锚定日期和技术指标的字典，结构为:
        {
            'date': str,  # 最后数据点的日期，格式为YYYYMMDD
            'indicators': dict  # 包含各项技术指标的字典
        }

    Raises:
        ValueError: 当参数无效或数据获取失败时
        RuntimeError: 当指标计算失败时
    """
    # 参数验证
    if not symbol:
        raise ValueError("股票代码不能为空")

    # 根据target_date计算最近的报告期
    if target_date:
        report_date = _get_latest_report_date(target_date)
    else:
        # 如果没有指定target_date，使用当前日期计算最近的报告期
        current_date = datetime.now().strftime("%Y%m%d")
        report_date = _get_latest_report_date(current_date)

    try:
        # 获取利润表数据
        df = ak.stock_lrb_em(date=report_date)

        if df.empty:
            raise ValueError(f"未获取到报告期 {report_date} 的利润表数据")

        # 根据股票代码过滤数据，支持三种格式匹配
        df_filtered = match_symbol_in_dataframe(df, "股票代码", symbol)

        if df_filtered.empty:
            # 如果没有找到对应股票的数据，返回全None指标
            indicators = _get_empty_indicators()
            return {"date": target_date or datetime.now().strftime("%Y%m%d"), "indicators": indicators}

        # 确保数据按股票代码排序
        df_filtered = df_filtered.sort_values("股票代码").reset_index(drop=True)

        # 如果指定了目标日期，筛选到目标日期为止的数据（这里主要用于兼容性）
        if target_date:
            # 注意：利润表数据没有明确的日期字段，所以此处仅作为占位符处理
            pass

        # 获取最后数据点的日期作为锚定时间（使用公告日期字段）
        last_announcement_date = df_filtered.iloc[-1]["公告日期"]
        if isinstance(last_announcement_date, str):
            anchor_date = last_announcement_date.replace("-", "")
        else:
            # 如果是datetime对象，转换为字符串格式
            anchor_date = last_announcement_date.strftime("%Y%m%d")

        # 计算各项财务指标
        indicators = {}

        # 使用第一行数据（应该只有一行，因为我们过滤了特定股票）
        if len(df_filtered) > 0:
            row = df_filtered.iloc[0]  # 取第一行数据

            # 1. 净利润率
            if pd.notna(row["净利润"]) and pd.notna(row["营业总收入"]):
                indicators["net_profit_margin"] = _calculate_net_profit_margin(row)

            # 2. 营业利润率
            if pd.notna(row["营业利润"]) and pd.notna(row["营业总收入"]):
                indicators["operating_profit_margin"] = _calculate_operating_profit_margin(row)

            # 3. EBITDA利润率
            required_fields = [
                "营业利润",
                "营业总支出-营业支出",
                "营业总支出-管理费用",
                "营业总支出-销售费用",
                "营业总支出-财务费用",
                "营业总收入",
            ]
            if all(pd.notna(row[field]) for field in required_fields):
                indicators["ebitda_margin"] = _calculate_ebitda_margin(row)

            # 4. 毛利率
            if pd.notna(row["营业总收入"]) and pd.notna(row["营业总支出-营业支出"]):
                indicators["gross_profit_margin"] = _calculate_gross_profit_margin(row)

            # 5. 利润质量比率
            if pd.notna(row["净利润"]) and pd.notna(row["营业利润"]):
                indicators["profit_quality_ratio"] = _calculate_profit_quality_ratio(row)

            # 6. 营业费用率
            required_fields = [
                "营业总支出-销售费用",
                "营业总支出-管理费用",
                "营业总支出-财务费用",
                "营业总收入",
            ]
            if all(pd.notna(row[field]) for field in required_fields):
                indicators["operating_expense_ratio"] = _calculate_operating_expense_ratio(row)

            # 7. 净利润同比增长率
            if pd.notna(row["净利润同比"]):
                indicators["net_profit_growth_rate"] = _calculate_net_profit_growth_rate(row)

            # 8. 营业收入同比增长率
            if pd.notna(row["营业总收入同比"]):
                indicators["revenue_growth_rate"] = _calculate_revenue_growth_rate(row)

            # 9. 利润杠杆指数
            required_fields = ["净利润同比", "营业总收入同比"]
            if all(pd.notna(row[field]) for field in required_fields):
                indicators["profit_leverage_index"] = _calculate_profit_leverage_index(row)

            # 10. 利润安全边际
            if pd.notna(row["营业利润"]) and pd.notna(row["净利润"]):
                indicators["profit_safety_margin"] = _calculate_profit_safety_margin(row)

        # 添加元数据到indicators字典
        indicators["report_date"] = report_date
        indicators["calculation_date"] = target_date or datetime.now().strftime(
            "%Y%m%d"
        )
        indicators["data_points"] = len(df_filtered)

        # 返回新的结构：包含锚定日期和指标字典
        return {"date": anchor_date, "indicators": indicators}

    except Exception as e:
        raise RuntimeError(f"计算利润表财务指标失败: {str(e)}")


def _get_empty_indicators() -> dict[str, Any]:
    """返回全None的指标字典，用于没有找到股票数据的情况"""
    return {
        "net_profit_margin": None,
        "operating_profit_margin": None,
        "ebitda_margin": None,
        "gross_profit_margin": None,
        "profit_quality_ratio": None,
        "operating_expense_ratio": None,
        "net_profit_growth_rate": None,
        "revenue_growth_rate": None,
        "profit_leverage_index": None,
        "profit_safety_margin": None,
        "report_date": None,
        "calculation_date": datetime.now().strftime("%Y%m%d"),
        "data_points": 0,
    }


def _get_latest_report_date(target_date: str) -> str:
    """
    根据目标日期计算最近的报告期

    Args:
        target_date: 目标日期，格式为YYYYMMDD

    Returns:
        最近的报告期，格式为YYYYMMDD，可能的值为XXXX0331、XXXX0630、XXXX0930、XXXX1231
    """
    from datetime import datetime

    # 解析目标日期
    target_dt = datetime.strptime(target_date, "%Y%m%d")
    year = target_dt.year
    month = target_dt.month
    day = target_dt.day

    # 定义季度末日期
    q1_end = f"{year}0331"  # 第一季度末
    q2_end = f"{year}0630"  # 第二季度末
    q3_end = f"{year}0930"  # 第三季度末
    q4_end = f"{year}1231"  # 第四季度末

    # 根据目标日期确定最近的报告期
    if month <= 3 or (month == 3 and day <= 31):
        # 如果在第一季度，使用上一年的年报
        return f"{year-1}1231"
    elif month <= 6 or (month == 6 and day <= 30):
        # 如果在第二季度，使用第一季度报告
        return q1_end
    elif month <= 9 or (month == 9 and day <= 30):
        # 如果在第三季度，使用第二季度报告
        return q2_end
    else:
        # 如果在第四季度，使用第三季度报告
        return q3_end


def _calculate_net_profit_margin(row: pd.Series) -> float:
    """计算净利润率
    公式: 净利润 / 营业总收入 * 100
    """
    if row["营业总收入"] == 0:
        return 0.0

    return (row["净利润"] / row["营业总收入"]) * 100


def _calculate_operating_profit_margin(row: pd.Series) -> float:
    """计算营业利润率
    公式: 营业利润 / 营业总收入 * 100
    """
    if row["营业总收入"] == 0:
        return 0.0

    return (row["营业利润"] / row["营业总收入"]) * 100


def _calculate_ebitda_margin(row: pd.Series) -> float:
    """计算EBITDA利润率
    公式: (营业利润 + 营业总支出-管理费用 + 营业总支出-销售费用 + 营业总支出-财务费用) / 营业总收入 * 100
    """
    ebitda = (
        row["营业利润"]
        + row["营业总支出-管理费用"]
        + row["营业总支出-销售费用"]
        + row["营业总支出-财务费用"]
    )

    if row["营业总收入"] == 0:
        return 0.0

    return (ebitda / row["营业总收入"]) * 100


def _calculate_gross_profit_margin(row: pd.Series) -> float:
    """计算毛利率
    公式: (营业总收入 - 营业总支出-营业支出) / 营业总收入 * 100
    """
    gross_profit = row["营业总收入"] - row["营业总支出-营业支出"]

    if row["营业总收入"] == 0:
        return 0.0

    return (gross_profit / row["营业总收入"]) * 100


def _calculate_profit_quality_ratio(row: pd.Series) -> float:
    """计算利润质量比率
    公式: 净利润 / 营业利润 * 100
    """
    if row["营业利润"] == 0:
        return 0.0

    return (row["净利润"] / row["营业利润"]) * 100


def _calculate_operating_expense_ratio(row: pd.Series) -> float:
    """计算营业费用率
    公式: (营业总支出-销售费用 + 营业总支出-管理费用 + 营业总支出-财务费用) / 营业总收入 * 100
    """
    total_expenses = (
        row["营业总支出-销售费用"]
        + row["营业总支出-管理费用"]
        + row["营业总支出-财务费用"]
    )

    if row["营业总收入"] == 0:
        return 0.0

    return (total_expenses / row["营业总收入"]) * 100


def _calculate_net_profit_growth_rate(row: pd.Series) -> float:
    """计算净利润同比增长率
    公式: 净利润同比
    """
    return row["净利润同比"]


def _calculate_revenue_growth_rate(row: pd.Series) -> float:
    """计算营业收入同比增长率
    公式: 营业总收入同比
    """
    return row["营业总收入同比"]


def _calculate_profit_leverage_index(row: pd.Series) -> float:
    """计算利润杠杆指数
    公式: 净利润同比 / 营业总收入同比
    """
    if row["营业总收入同比"] == 0:
        return 0.0

    return row["净利润同比"] / row["营业总收入同比"]


def _calculate_profit_safety_margin(row: pd.Series) -> float:
    """计算利润安全边际
    公式: (营业利润 - 净利润) / 净利润 * 100
    """
    if row["净利润"] == 0:
        return 0.0

    return ((row["营业利润"] - row["净利润"]) / row["净利润"]) * 100
