"""
东方财富-数据中心-年报季报-预约披露时间指标生成函数

基于akshare的stock_yysj_em API，计算上市公司报告披露相关指标
"""

from datetime import datetime
from typing import Any

import akshare as ak
import pandas as pd


def calculate_stock_yysj_em_indicators(
    symbol: str = "", date: str = "", target_date: str | None = None
) -> dict[str, Any]:
    """
    计算东方财富-数据中心-年报季报-预约披露时间相关指标

    Args:
        symbol: 股票类型，可选值包括{'沪深A股', '沪市A股', '科创板', '深市A股', '创业板', '京市A股', 'ST板'}
        date: 报告日期，格式为"XXXX0331", "XXXX0630", "XXXX0930", "XXXX1231"
        target_date: 目标日期，格式为YYYYMMDD，用于筛选数据

    Returns:
        包含锚定日期和技术指标的字典，结构为:
        {
            'date': str,  # 最后数据点的日期，格式为YYYYMMDD
            'indicators': dict  # 包含各项技术指标的字典
        }

    Raises:
        ValueError: 当参数无效或数据获取失败时
        RuntimeError: 当指标计算失败时
    """
    # 参数验证
    if not isinstance(symbol, str):
        raise ValueError("symbol参数必须为字符串类型")

    if not isinstance(date, str):
        raise ValueError("date参数必须为字符串类型")

    try:
        # 获取股票历史数据
        df = ak.stock_yysj_em(symbol=symbol, date=date)

        if df is None or df.empty:
            # 返回空指标而不是抛出异常
            indicators = _get_empty_indicators()
            return {"date": target_date or datetime.now().strftime("%Y%m%d"), "indicators": indicators}

        # 检查必要的列是否存在
        required_columns = ["首次预约时间", "实际披露时间"]
        if not all(col in df.columns for col in required_columns):
            indicators = _get_empty_indicators()
            return {"date": target_date or datetime.now().strftime("%Y%m%d"), "indicators": indicators}

        # 确保数据按日期排序
        df = df.sort_values("首次预约时间").reset_index(drop=True)

        # 如果指定了目标日期，筛选到目标日期为止的数据
        if target_date:
            target_datetime = pd.to_datetime(target_date, format="%Y%m%d")
            df["首次预约时间"] = pd.to_datetime(df["首次预约时间"], errors="coerce")
            df["实际披露时间"] = pd.to_datetime(df["实际披露时间"], errors="coerce")
            df = df[df["首次预约时间"] <= target_datetime]

            if df.empty:
                raise ValueError(
                    f"在目标日期 {target_date} 之前未找到{symbol}在{date}的数据"
                )

        # 获取最后数据点的日期作为锚定时间
        last_date = df.iloc[-1]["首次预约时间"]
        if isinstance(last_date, str):
            anchor_date = last_date
        else:
            # 如果是datetime对象，转换为字符串格式
            anchor_date = last_date.strftime("%Y%m%d")

        # 计算各项技术指标
        indicators = {}

        # 1. 报告披露及时性指标 (需要首次预约时间和实际披露时间)
        if len(df) >= 1:
            indicators["report_disclosure_timeliness"] = (
                _calculate_report_disclosure_timeliness(df)
            )

        # 2. 报告披露时间变更频率指标 (需要所有变更日期字段)
        if len(df) >= 1:
            indicators["report_disclosure_change_frequency"] = (
                _calculate_report_disclosure_change_frequency(df)
            )

        # 3. 报告披露延迟天数 (需要首次预约时间和实际披露时间)
        if len(df) >= 1:
            indicators["report_disclosure_delay_days"] = (
                _calculate_report_disclosure_delay_days(df)
            )

        # 4. 报告披露一致性指标 (需要所有变更日期字段)
        if len(df) >= 1:
            indicators["report_disclosure_consistency"] = (
                _calculate_report_disclosure_consistency(df)
            )

        # 5. 提前披露率 (需要首次预约时间和实际披露时间)
        if len(df) >= 1:
            indicators["early_disclosure_rate"] = _calculate_early_disclosure_rate(df)

        # 添加元数据到indicators字典
        indicators["symbol"] = symbol
        indicators["date"] = date
        indicators["calculation_date"] = target_date or datetime.now().strftime(
            "%Y%m%d"
        )
        indicators["data_points"] = len(df)

        # 返回新的结构：包含锚定日期和指标字典
        return {"date": anchor_date, "indicators": indicators}

    except Exception as e:
        raise RuntimeError(f"计算{symbol}在{date}报告披露指标失败: {str(e)}")


def _calculate_report_disclosure_timeliness(df: pd.DataFrame) -> float:
    """计算报告披露及时性指标
    公式: if (实际披露时间 >= 首次预约时间, 实际披露时间 - 首次预约时间, 0)
    """
    # 检查数据长度是否满足要求
    if len(df) < 1:
        return 0.0

    try:
        # 转换日期字段为datetime类型
        df["首次预约时间"] = pd.to_datetime(df["首次预约时间"], errors="coerce")
        df["实际披露时间"] = pd.to_datetime(df["实际披露时间"], errors="coerce")

        # 获取最后一个股票的数据
        last_row = df.iloc[-1]

        if pd.isna(last_row["首次预约时间"]) or pd.isna(last_row["实际披露时间"]):
            return 0.0

        # 计算延迟天数（如果实际披露时间晚于预约时间）
        delay_days = (last_row["实际披露时间"] - last_row["首次预约时间"]).days

        # 如果实际披露时间早于或等于预约时间，则返回0
        return max(0, delay_days)

    except Exception:
        return 0.0


def _calculate_report_disclosure_change_frequency(df: pd.DataFrame) -> float:
    """计算报告披露时间变更频率指标
    公式: sum(1 for 每次变更日期 if 变更日期 != '无' and 变更日期 != '0000-00-00')
    """
    # 检查数据长度是否满足要求
    if len(df) < 1:
        return 0.0

    try:
        # 获取最后一个股票的数据
        last_row = df.iloc[-1]

        # 统计有效变更日期的个数
        change_count = 0

        # 检查一次变更日期
        if (
            not pd.isna(last_row["一次变更日期"])
            and str(last_row["一次变更日期"]) != "无"
            and str(last_row["一次变更日期"]) != "0000-00-00"
        ):
            change_count += 1

        # 检查二次变更日期
        if (
            not pd.isna(last_row["二次变更日期"])
            and str(last_row["二次变更日期"]) != "无"
            and str(last_row["二次变更日期"]) != "0000-00-00"
        ):
            change_count += 1

        # 检查三次变更日期
        if (
            not pd.isna(last_row["三次变更日期"])
            and str(last_row["三次变更日期"]) != "无"
            and str(last_row["三次变更日期"]) != "0000-00-00"
        ):
            change_count += 1

        return float(change_count)

    except Exception:
        return 0.0


def _calculate_report_disclosure_delay_days(df: pd.DataFrame) -> float:
    """计算报告披露延迟天数
    公式: (实际披露时间 - 首次预约时间).days
    """
    # 检查数据长度是否满足要求
    if len(df) < 1:
        return 0.0

    try:
        # 转换日期字段为datetime类型
        df["首次预约时间"] = pd.to_datetime(df["首次预约时间"], errors="coerce")
        df["实际披露时间"] = pd.to_datetime(df["实际披露时间"], errors="coerce")

        # 获取最后一个股票的数据
        last_row = df.iloc[-1]

        if pd.isna(last_row["首次预约时间"]) or pd.isna(last_row["实际披露时间"]):
            return 0.0

        # 计算延迟天数（正数表示延迟，负数表示提前）
        delay_days = (last_row["实际披露时间"] - last_row["首次预约时间"]).days
        return float(delay_days)

    except Exception:
        return 0.0


def _calculate_report_disclosure_consistency(df: pd.DataFrame) -> float:
    """计算报告披露一致性指标
    公式: std(所有有效变更日期的天数差，以首次预约时间为基准)
    """
    # 检查数据长度是否满足要求
    if len(df) < 1:
        return 0.0

    try:
        # 转换日期字段为datetime类型
        df["首次预约时间"] = pd.to_datetime(df["首次预约时间"], errors="coerce")
        df["一次变更日期"] = pd.to_datetime(df["一次变更日期"], errors="coerce")
        df["二次变更日期"] = pd.to_datetime(df["二次变更日期"], errors="coerce")
        df["三次变更日期"] = pd.to_datetime(df["三次变更日期"], errors="coerce")

        # 获取最后一个股票的数据
        last_row = df.iloc[-1]

        if pd.isna(last_row["首次预约时间"]):
            return 0.0

        # 收集所有有效变更日期与首次预约时间的天数差
        days_diff_list = []

        # 检查一次变更日期
        if not pd.isna(last_row["一次变更日期"]):
            diff = (last_row["一次变更日期"] - last_row["首次预约时间"]).days
            days_diff_list.append(float(diff))

        # 检查二次变更日期
        if not pd.isna(last_row["二次变更日期"]):
            diff = (last_row["二次变更日期"] - last_row["首次预约时间"]).days
            days_diff_list.append(float(diff))

        # 检查三次变更日期
        if not pd.isna(last_row["三次变更日期"]):
            diff = (last_row["三次变更日期"] - last_row["首次预约时间"]).days
            days_diff_list.append(float(diff))

        # 如果没有有效变更日期，返回0
        if len(days_diff_list) == 0:
            return 0.0

        # 计算标准差
        import numpy as np

        return float(np.std(days_diff_list, ddof=1))  # 使用样本标准差

    except Exception:
        return 0.0


def _calculate_early_disclosure_rate(df: pd.DataFrame) -> float:
    """计算提前披露率
    公式: 1 if 实际披露时间 < 首次预约时间 else 0
    """
    # 检查数据长度是否满足要求
    if len(df) < 1:
        return 0.0

    try:
        # 转换日期字段为datetime类型
        df["首次预约时间"] = pd.to_datetime(df["首次预约时间"], errors="coerce")
        df["实际披露时间"] = pd.to_datetime(df["实际披露时间"], errors="coerce")

        # 获取最后一个股票的数据
        last_row = df.iloc[-1]

        if pd.isna(last_row["首次预约时间"]) or pd.isna(last_row["实际披露时间"]):
            return 0.0

        # 判断是否提前披露
        if last_row["实际披露时间"] < last_row["首次预约时间"]:
            return 1.0
        else:
            return 0.0

    except Exception:
        return 0.0
